import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  FiHome,
  FiMail,
  FiPhone,
  FiMapPin,
  FiGlobe,
  FiUsers,
  FiAward,
  FiSave,
  FiSend,
  FiAlertCircle,
  FiCheck,
  FiX,
  FiClock,
  FiEdit3,
  FiEye,
  FiFileText
} from 'react-icons/fi';
import {
  fetchInstituteProfile,
  fetchProfileStatus,
  saveInstituteProfile,
  saveInstituteProfileWithDocuments,
  submitForApproval,
  selectProfile,
  selectProfileLoading,
  selectProfileError,
  selectProfileNotFound,
  selectSaveLoading,
  selectSaveError,
  selectSaveSuccess,
  selectSubmitLoading,
  selectSubmitError,
  selectSubmitSuccess,
  selectApprovalStatus,
  selectIsProfileComplete,
  selectRejectionReason,
  clearErrors,
  clearSuccessStates
} from '../../store/slices/InstituteProfileSlice';
import { LoadingSpinner } from '../../components/ui';
import InstituteDocumentUpload from '../../components/institute/InstituteDocumentUpload';
import { getErrorMessage } from '../../utils/helpers/errorHandler';

const InstituteSettings = () => {
  const dispatch = useDispatch();

  // Redux state
  const profile = useSelector(selectProfile);
  const profileLoading = useSelector(selectProfileLoading);
  const profileError = useSelector(selectProfileError);
  const profileNotFound = useSelector(selectProfileNotFound);
  const saveLoading = useSelector(selectSaveLoading);
  const saveError = useSelector(selectSaveError);
  const saveSuccess = useSelector(selectSaveSuccess);
  const submitLoading = useSelector(selectSubmitLoading);
  const submitError = useSelector(selectSubmitError);
  const submitSuccess = useSelector(selectSubmitSuccess);
  const approvalStatus = useSelector(selectApprovalStatus);
  const isProfileComplete = useSelector(selectIsProfileComplete);
  const rejectionReason = useSelector(selectRejectionReason);

  // Local state - Simplified to match backend fields only
  const [formData, setFormData] = useState({
    institute_name: '',
    description: '',
    address: '',
    city: '',
    state: '',
    postal_code: '',
    website: '',
    established_year: 0,
    institute_type: 'university',
    accreditation: '',
    linkedin_url: '',
    facebook_url: '',
    twitter_url: '',
    logo_url: '',
    banner_url: ''
  });

  const [activeTab, setActiveTab] = useState('basic');
  const [isEditing, setIsEditing] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [fieldErrors, setFieldErrors] = useState({});
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  // Load profile on component mount
  useEffect(() => {
    // Don't fetch if we already have a profile, an error, know it doesn't exist, or if already loading
    if (!profile && !profileError && !profileNotFound && !profileLoading) {
      dispatch(fetchInstituteProfile());
    }
  }, [dispatch, profile, profileError, profileNotFound, profileLoading]);

  // Update form data when profile is loaded
  useEffect(() => {
    if (profile) {
      setFormData({
        institute_name: profile.institute_name || '',
        description: profile.description || '',
        address: profile.address || '',
        city: profile.city || '',
        state: profile.state || '',
        postal_code: profile.postal_code || '',
        website: profile.website || '',
        established_year: profile.established_year || 0,
        institute_type: profile.institute_type || 'university',
        accreditation: profile.accreditation || '',
        linkedin_url: profile.linkedin_url || '',
        facebook_url: profile.facebook_url || '',
        twitter_url: profile.twitter_url || '',
        logo_url: profile.logo_url || '',
        banner_url: profile.banner_url || ''
      });

      // Load existing documents if available
      if (profile.documents) {
        setDocuments(profile.documents);
      }

      // If profile doesn't exist, enable editing mode
      if (!profile.institute_name) {
        setIsEditing(true);
      }
    }
  }, [profile]);

  // Handle profile not found case
  useEffect(() => {
    if (profileNotFound) {
      setIsEditing(true); // Enable editing mode for new profile creation
    }
  }, [profileNotFound]);

  // Handle success messages
  useEffect(() => {
    if (saveSuccess || submitSuccess) {
      setShowSuccessMessage(true);
      setIsEditing(false);
      const timer = setTimeout(() => {
        setShowSuccessMessage(false);
        dispatch(clearSuccessStates());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [saveSuccess, submitSuccess, dispatch]);

  // Define mandatory fields
  const mandatoryFields = {
    institute_name: 'Institute Name',
    institute_type: 'Institute Type',
    description: 'Description',
    address: 'Address',
    city: 'City'
  };

  // Validate individual field
  const validateField = (name, value) => {
    if (mandatoryFields[name]) {
      if (!value || (typeof value === 'string' && value.trim() === '')) {
        return `${mandatoryFields[name]} is required`;
      }
    }
    return null;
  };

  // Validate all mandatory fields
  const validateForm = () => {
    const errors = {};
    let isValid = true;

    Object.keys(mandatoryFields).forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) {
        errors[field] = error;
        isValid = false;
      }
    });

    setFieldErrors(errors);
    return isValid;
  };

  // Check if form is valid for create button
  const isFormValidForCreate = () => {
    return Object.keys(mandatoryFields).every(field => {
      const value = formData[field];
      return value && (typeof value !== 'string' || value.trim() !== '');
    });
  };

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    const newValue = name === 'established_year' ? parseInt(value) || 0 : value;

    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }));

    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }

    // Real-time validation for mandatory fields
    if (hasAttemptedSubmit && mandatoryFields[name]) {
      const error = validateField(name, newValue);
      setFieldErrors(prev => ({
        ...prev,
        [name]: error
      }));
    }
  };



  // Save profile
  const handleSave = async () => {
    setHasAttemptedSubmit(true);

    // For create profile, validate mandatory fields
    if (profileNotFound && !validateForm()) {
      // Scroll to first error field
      const firstErrorField = Object.keys(fieldErrors)[0];
      if (firstErrorField) {
        const element = document.querySelector(`[name="${firstErrorField}"]`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.focus();
        }
      }
      return;
    }

    try {
      // Check if there are new documents to upload
      const newDocuments = documents.filter(doc => doc.file);

      if (newDocuments.length > 0) {
        // Use the document upload endpoint
        await dispatch(saveInstituteProfileWithDocuments({
          profileData: formData,
          documents: newDocuments
        })).unwrap();
      } else {
        // Use the regular profile update endpoint
        await dispatch(saveInstituteProfile(formData)).unwrap();
      }

      // Reset validation state on successful save
      setHasAttemptedSubmit(false);
      setFieldErrors({});
    } catch (error) {
      console.error('Failed to save profile:', error);
    }
  };

  // Submit for approval
  const handleSubmitForApproval = async () => {
    if (!isProfileComplete) {
      alert('Please complete all required fields before submitting for approval.');
      return;
    }

    try {
      await dispatch(submitForApproval()).unwrap();
    } catch (error) {
      console.error('Failed to submit for approval:', error);
    }
  };

  // Get approval status display
  const getApprovalStatusDisplay = () => {
    switch (approvalStatus) {
      case 'draft':
        return {
          color: 'text-gray-600',
          bg: 'bg-gray-100',
          icon: FiEdit3,
          text: 'Draft - Complete your profile'
        };
      case 'pending':
        return {
          color: 'text-yellow-600',
          bg: 'bg-yellow-100',
          icon: FiClock,
          text: 'Pending Admin Approval'
        };
      case 'approved':
        return {
          color: 'text-green-600',
          bg: 'bg-green-100',
          icon: FiCheck,
          text: 'Approved - Full Access Granted'
        };
      case 'rejected':
        return {
          color: 'text-red-600',
          bg: 'bg-red-100',
          icon: FiX,
          text: 'Rejected - Please Review and Resubmit'
        };
      default:
        return {
          color: 'text-gray-600',
          bg: 'bg-gray-100',
          icon: FiEdit3,
          text: 'Unknown Status'
        };
    }
  };

  const statusDisplay = getApprovalStatusDisplay();

  if (profileLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Simple header for profile creation
  const renderProfileNotFoundHeader = () => {
    if (!profileNotFound) return null;

    return (
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Create Institute Profile</h1>
        <p className="text-gray-600">
          Complete the form below to create your institute profile.
        </p>
      </div>
    );
  };

  const tabs = [
    { id: 'basic', label: 'Basic Information', icon: FiHome },
    { id: 'details', label: 'Institute Details', icon: FiFileText },
    { id: 'social', label: 'Social Media & Branding', icon: FiGlobe },
    { id: 'documents', label: 'Verification Documents', icon: FiFileText }
  ];

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header - conditional based on profile status */}
      {profileNotFound ? renderProfileNotFoundHeader() : (
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Institute Profile</h1>
          <p className="text-gray-600">
            Complete your institute profile to get admin approval and access all features.
          </p>
        </div>
      )}

      {/* Approval Status Banner - only show when profile exists */}
      {!profileNotFound && (
        <div className={`${statusDisplay.bg} border border-gray-200 rounded-lg p-4 mb-6`}>
        <div className="flex items-center">
          <statusDisplay.icon className={`h-5 w-5 ${statusDisplay.color} mr-3`} />
          <div className="flex-1">
            <h3 className={`font-medium ${statusDisplay.color}`}>
              {statusDisplay.text}
            </h3>
            {rejectionReason && approvalStatus === 'rejected' && (
              <p className="text-sm text-red-600 mt-1">
                Reason: {rejectionReason}
              </p>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            {approvalStatus === 'draft' && isProfileComplete && (
              <button
                onClick={handleSubmitForApproval}
                disabled={submitLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {submitLoading ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <FiSend className="h-4 w-4 mr-2" />
                )}
                Submit for Approval
              </button>
            )}

            {(approvalStatus === 'draft' || approvalStatus === 'rejected') && (
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiEdit3 className="h-4 w-4 mr-2" />
                {isEditing ? 'Cancel Edit' : 'Edit Profile'}
              </button>
            )}
          </div>
        </div>
        </div>
      )}

      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiCheck className="h-5 w-5 text-green-600 mr-3" />
            <p className="text-green-700">
              {saveSuccess && 'Profile saved successfully!'}
              {submitSuccess && 'Profile submitted for admin approval!'}
            </p>
          </div>
        </div>
      )}

      {/* Error Messages - hide profile not found errors since we handle them in header */}
      {(profileError || saveError || submitError) && !profileNotFound && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <FiAlertCircle className="h-5 w-5 text-red-600 mr-3" />
            <p className="text-red-700">
              {getErrorMessage(profileError || saveError || submitError)}
            </p>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
                {profileNotFound && (
                  <div className="text-sm text-gray-600">
                    {(() => {
                      const completedFields = Object.keys(mandatoryFields).filter(field => {
                        const value = formData[field];
                        return value && (typeof value !== 'string' || value.trim() !== '');
                      }).length;
                      const totalFields = Object.keys(mandatoryFields).length;
                      return (
                        <span className={`font-medium ${completedFields === totalFields ? 'text-green-600' : 'text-amber-600'}`}>
                          {completedFields}/{totalFields} required fields completed
                        </span>
                      );
                    })()}
                  </div>
                )}
              </div>

              {/* Progress bar for profile creation */}
              {profileNotFound && (
                <div className="bg-gray-50 rounded-lg p-4">
                  {(() => {
                    const completedFields = Object.keys(mandatoryFields).filter(field => {
                      const value = formData[field];
                      return value && (typeof value !== 'string' || value.trim() !== '');
                    }).length;
                    const totalFields = Object.keys(mandatoryFields).length;
                    const progressPercentage = (completedFields / totalFields) * 100;

                    return (
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700">Profile Completion</span>
                          <span className="text-sm text-gray-600">{Math.round(progressPercentage)}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${
                              progressPercentage === 100 ? 'bg-green-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${progressPercentage}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">
                          {progressPercentage === 100
                            ? 'All required fields completed! You can now create your profile.'
                            : 'Fill in all required fields to enable profile creation.'
                          }
                        </p>
                      </div>
                    );
                  })()}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Institute Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="institute_name"
                    value={formData.institute_name}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 disabled:bg-gray-100 ${
                      fieldErrors.institute_name
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                    }`}
                    placeholder="Enter institute name"
                  />
                  {fieldErrors.institute_name && (
                    <p className="mt-1 text-sm text-red-600">{fieldErrors.institute_name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Established Year
                  </label>
                  <input
                    type="number"
                    name="established_year"
                    value={formData.established_year}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    placeholder="e.g., 1995"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Institute Type <span className="text-red-500">*</span>
                  </label>
                  <select
                    name="institute_type"
                    value={formData.institute_type}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 disabled:bg-gray-100 ${
                      fieldErrors.institute_type
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                    }`}
                  >
                    <option value="university">University</option>
                    <option value="college">College</option>
                    <option value="school">School</option>
                    <option value="institute">Institute</option>
                    <option value="academy">Academy</option>
                  </select>
                  {fieldErrors.institute_type && (
                    <p className="mt-1 text-sm text-red-600">{fieldErrors.institute_type}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Website URL
                  </label>
                  <input
                    type="url"
                    name="website"
                    value={formData.website}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    placeholder="https://www.example.com"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description <span className="text-red-500">*</span>
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  rows={4}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 disabled:bg-gray-100 ${
                    fieldErrors.description
                      ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                      : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  }`}
                  placeholder="Describe your institute, its mission, and what makes it unique..."
                />
                {fieldErrors.description && (
                  <p className="mt-1 text-sm text-red-600">{fieldErrors.description}</p>
                )}
              </div>

              {/* Address Section */}
              <div className="space-y-4">
                <h4 className="text-md font-medium text-gray-900">Address Information</h4>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Address <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 disabled:bg-gray-100 ${
                      fieldErrors.address
                        ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                    }`}
                    placeholder="123 Main Street"
                  />
                  {fieldErrors.address && (
                    <p className="mt-1 text-sm text-red-600">{fieldErrors.address}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      City <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 disabled:bg-gray-100 ${
                        fieldErrors.city
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500'
                          : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                      }`}
                      placeholder="City"
                    />
                    {fieldErrors.city && (
                      <p className="mt-1 text-sm text-red-600">{fieldErrors.city}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      State/Province
                    </label>
                    <input
                      type="text"
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                      placeholder="State"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Postal Code
                    </label>
                    <input
                      type="text"
                      name="postal_code"
                      value={formData.postal_code}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                      placeholder="12345"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}


          {activeTab === 'details' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Institute Details</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Accreditation
                </label>
                <input
                  type="text"
                  name="accreditation"
                  value={formData.accreditation}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="Accreditation details"
                />
              </div>
            </div>
          )}

          {activeTab === 'social' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">Social Media & Branding</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    LinkedIn URL
                  </label>
                  <input
                    type="url"
                    name="linkedin_url"
                    value={formData.linkedin_url}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    placeholder="https://linkedin.com/company/your-institute"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Facebook URL
                  </label>
                  <input
                    type="url"
                    name="facebook_url"
                    value={formData.facebook_url}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    placeholder="https://facebook.com/your-institute"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Twitter URL
                  </label>
                  <input
                    type="url"
                    name="twitter_url"
                    value={formData.twitter_url}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    placeholder="https://twitter.com/your-institute"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Logo URL
                  </label>
                  <input
                    type="url"
                    name="logo_url"
                    value={formData.logo_url}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    placeholder="https://example.com/logo.png"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Banner URL
                  </label>
                  <input
                    type="url"
                    name="banner_url"
                    value={formData.banner_url}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                    placeholder="https://example.com/banner.png"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'documents' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900">Verification Documents</h3>
                <span className="text-sm text-gray-500">
                  Upload documents for admin verification
                </span>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <FiAlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Document Requirements</p>
                    <ul className="list-disc list-inside space-y-1 text-blue-700">
                      <li>Upload official documents for verification (accreditation, licenses, certificates)</li>
                      <li>Supported formats: PDF, DOC, DOCX, TXT, RTF, ODT</li>
                      <li>Maximum file size: 20MB per document</li>
                      <li>Documents will be reviewed by admin for verification</li>
                    </ul>
                  </div>
                </div>
              </div>

              <InstituteDocumentUpload
                documents={documents}
                onDocumentsChange={setDocuments}
                maxFileSize={20}
              />
            </div>
          )}

          {/* Save and Submit Buttons */}
          {isEditing && (
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <button
                onClick={handleSave}
                disabled={saveLoading || (profileNotFound && !isFormValidForCreate())}
                title={
                  profileNotFound && !isFormValidForCreate()
                    ? 'Please fill in all required fields to create your profile'
                    : ''
                }
                className={`inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 ${
                  saveLoading || (profileNotFound && !isFormValidForCreate())
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {saveLoading ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <FiSave className="h-5 w-5 mr-2" />
                )}
                {profileNotFound ? 'Create Profile' : 'Update Profile'}
              </button>

              {/* Send to Admin for Review Button - Show after profile is created/updated and when profile is complete */}
              {!profileNotFound && isProfileComplete && approvalStatus === 'draft' && (
                <button
                  onClick={handleSubmitForApproval}
                  disabled={submitLoading}
                  className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  {submitLoading ? (
                    <LoadingSpinner size="sm" className="mr-2" />
                  ) : (
                    <FiSend className="h-5 w-5 mr-2" />
                  )}
                  Send to Admin for Review
                </button>
              )}
            </div>

            {/* Helpful message for create profile */}
            {profileNotFound && !isFormValidForCreate() && (
              <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <FiAlertCircle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-amber-800">
                    <p className="font-medium mb-1">Complete Required Fields</p>
                    <p>Please fill in all mandatory fields (marked with *) to create your institute profile:</p>
                    <ul className="list-disc list-inside mt-1 space-y-0.5">
                      {Object.entries(mandatoryFields).map(([field, label]) => (
                        !formData[field] || (typeof formData[field] === 'string' && formData[field].trim() === '') ? (
                          <li key={field} className="text-amber-700">{label}</li>
                        ) : null
                      )).filter(Boolean)}
                    </ul>
                  </div>
                </div>
              </div>
            )}
          )}
        </div>
      </div>
    </div>
  );
};

export default InstituteSettings;